{"name": "sub-dream", "type": "module", "scripts": {"dev": "wrangler dev", "dev:bun": "bun run --hot src/index.ts", "deploy": "wrangler deploy --minify", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "build:cli": "bun build src/cli.ts --compile --outfile sub-dream", "cli": "bun run src/cli.ts"}, "dependencies": {"hono": "^4.9.6"}, "devDependencies": {"@types/node": "^24.3.1", "wrangler": "^4.4.0"}}