import { Hono } from 'hono';
import { renderForSingbox } from './render';
import { getMergedSubscriptionNodes } from './subscription';

type Bindings = CloudflareBindings;

const app = new Hono<{ Bindings: Bindings }>();

app.get('/', (c) => {
  return c.text('Hello Hono!');
});

app.get('/api/manifest', async (c) => {
  const token = c.req.query('token') || c.req.header('Authorization')?.replace('Bearer ', '');
  if (!token || token !== c.env['MY_SECRET']) {
    return c.text('Unauthorized', 401);
  }
  const subscriptionNodes = await getMergedSubscriptionNodes('sing-box', {
    ...c.env,
  });
  const res = renderForSingbox(subscriptionNodes);
  return c.json(res);
});

export default app;
