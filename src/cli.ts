#!/usr/bin/env node

import { readFileSync, writeFileSync } from 'fs';
import { resolve } from 'path';
import { getMergedSubscriptionNodes } from './subscription.js';
import { renderForSingbox } from './render.js';

interface CLIOptions {
  output?: string;
  type?: 'sing-box';
  help?: boolean;
}

function parseArgs(args: string[]): CLIOptions {
  const options: CLIOptions = {};
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '-o':
      case '--output':
        options.output = args[++i];
        break;
      case '-t':
      case '--type':
        options.type = args[++i] as 'sing-box';
        break;
      case '-h':
      case '--help':
        options.help = true;
        break;
    }
  }
  
  return options;
}

function showHelp() {
  console.log(`
Usage: node src/cli.ts [options]

Options:
  -o, --output <file>    Output file path (default: config.json)
  -t, --type <type>      Configuration type (default: sing-box)
  -h, --help             Show this help message

Examples:
  node src/cli.ts -o config.json -t sing-box
  node src/cli.ts --output ./output/config.json
`);
}

function loadEnvironmentVariables(): Record<string, string> {
  const secretMap: Record<string, string> = {};
  
  try {
    // Try to load from .env file
    const envPath = resolve(process.cwd(), '.env');
    const envContent = readFileSync(envPath, 'utf-8');
    
    envContent.split('\n').forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          secretMap[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
  } catch (error) {
    console.warn('Warning: Could not load .env file, using environment variables only');
  }
  
  // Override with actual environment variables
  Object.keys(process.env).forEach(key => {
    if (process.env[key]) {
      secretMap[key] = process.env[key]!;
    }
  });
  
  return secretMap;
}

async function pullAndRenderSubscription(type: 'sing-box', outputPath: string) {
  try {
    console.log(`Fetching subscription data for type: ${type}`);
    
    // Load environment variables
    const secretMap = loadEnvironmentVariables();
    
    // Validate required tokens
    const requiredTokens = ['ENET_TOKEN'];
    const missingTokens = requiredTokens.filter(token => !secretMap[token]);
    
    if (missingTokens.length > 0) {
      throw new Error(`Missing required environment variables: ${missingTokens.join(', ')}`);
    }
    
    // Fetch subscription nodes
    const subscriptionNodes = await getMergedSubscriptionNodes(type, secretMap);
    console.log(`Fetched ${subscriptionNodes.length} subscription nodes`);
    
    if (subscriptionNodes.length === 0) {
      console.warn('Warning: No subscription nodes found');
    }
    
    // Render configuration
    let renderedConfig: any;
    
    switch (type) {
      case 'sing-box':
        renderedConfig = renderForSingbox(subscriptionNodes);
        break;
      default:
        throw new Error(`Unsupported configuration type: ${type}`);
    }
    
    // Write to output file
    const configJson = JSON.stringify(renderedConfig, null, 2);
    writeFileSync(outputPath, configJson, 'utf-8');
    
    console.log(`Configuration successfully written to: ${outputPath}`);
    console.log(`Total outbounds: ${renderedConfig.outbounds?.length || 0}`);
    
  } catch (error) {
    console.error('Error:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

async function main() {
  const args = process.argv.slice(2);
  const options = parseArgs(args);
  
  if (options.help) {
    showHelp();
    return;
  }
  
  const outputPath = options.output || 'config.json';
  const type = options.type || 'sing-box';
  
  if (type !== 'sing-box') {
    console.error(`Error: Unsupported type '${type}'. Currently only 'sing-box' is supported.`);
    process.exit(1);
  }
  
  console.log(`Starting subscription pull and render...`);
  console.log(`Type: ${type}`);
  console.log(`Output: ${outputPath}`);
  console.log('');
  
  await pullAndRenderSubscription(type, outputPath);
}

// Run the CLI if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });
}

export { pullAndRenderSubscription, loadEnvironmentVariables };