#!/usr/bin/env node

import { readFileSync, writeFileSync } from 'fs';
import { resolve } from 'path';
import { getMergedSubscriptionNodes } from './subscription.js';
import { renderForSingbox } from './render.js';

interface CLIOptions {
  output?: string;
  type?: 'sing-box';
  help?: boolean;
}

function parseArgs(args: string[]): CLIOptions {
  const options: CLIOptions = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    switch (arg) {
      case '-o':
      case '--output':
        options.output = args[++i];
        break;
      case '-t':
      case '--type':
        options.type = args[++i] as 'sing-box';
        break;
      case '-h':
      case '--help':
        options.help = true;
        break;
    }
  }

  return options;
}

function showHelp() {
  console.log(`
Usage: sub-dream [options]

Options:
  -o, --output <path>    输出文件路径 (默认: config.json)
  -t, --type <type>      配置类型 (默认: sing-box)
  -h, --help             显示帮助信息

Examples:
  sub-dream                           # 生成 config.json
  sub-dream -o /path/to/config.json   # 指定输出路径
  sub-dream -t sing-box -o config.json # 指定类型和输出路径
`);
}

function loadEnvironmentVariables(): Record<string, string> {
  const secretMap: Record<string, string> = {};

  try {
    // Try to load from .env file
    const envPath = resolve(process.cwd(), '.env');
    const envContent = readFileSync(envPath, 'utf-8');

    envContent.split('\n').forEach((line: string) => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          secretMap[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
  } catch (error) {
    console.warn('警告: 无法加载 .env 文件，仅使用环境变量');
  }

  // Override with actual environment variables
  Object.keys(process.env).forEach(key => {
    const value = process.env[key];
    if (value && value.trim() !== '') {
      secretMap[key] = value;
    }
  });

  return secretMap;
}

async function pullAndRenderSubscription(type: 'sing-box', outputPath: string) {
  try {
    console.log(`正在拉取订阅数据，类型: ${type}`);

    // Load environment variables
    const secretMap = loadEnvironmentVariables();

    // Validate required tokens
    const requiredTokens = ['ENET_TOKEN'];
    const missingTokens = requiredTokens.filter(token => !secretMap[token] || secretMap[token].trim() === '');

    if (missingTokens.length > 0) {
      throw new Error(`缺少必需的环境变量: ${missingTokens.join(', ')}\n请在 .env 文件中设置这些变量，或通过环境变量传递。`);
    }

    // Fetch subscription nodes
    const subscriptionNodes = await getMergedSubscriptionNodes(type, secretMap);
    console.log(`已获取 ${subscriptionNodes.length} 个订阅节点`);

    if (subscriptionNodes.length === 0) {
      console.warn('警告: 未找到订阅节点');
    }

    // Render configuration
    let renderedConfig: any;

    switch (type) {
      case 'sing-box':
        renderedConfig = renderForSingbox(subscriptionNodes);
        break;
      default:
        throw new Error(`不支持的配置类型: ${type}`);
    }

    // Write to output file
    const configJson = JSON.stringify(renderedConfig, null, 2);
    writeFileSync(outputPath, configJson, 'utf-8');

    console.log(`配置已成功写入: ${outputPath}`);
    console.log(`总出站数量: ${renderedConfig.outbounds?.length || 0}`);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    if (errorMessage.includes('socket connection was closed') ||
        errorMessage.includes('Unable to connect')) {
      console.error('网络连接错误: 无法连接到订阅服务器');
      console.error('请检查:');
      console.error('1. 网络连接是否正常');
      console.error('2. 订阅 token 是否有效');
      console.error('3. 订阅服务是否可用');
    } else {
      console.error('错误:', errorMessage);
    }

    process.exit(1);
  }
}

async function main() {
  const args = process.argv.slice(2);
  const options = parseArgs(args);

  if (options.help) {
    showHelp();
    return;
  }

  const outputPath = options.output || 'config.json';
  const type = options.type || 'sing-box';

  if (type !== 'sing-box') {
    console.error(`错误: 不支持的类型 '${type}'。目前仅支持 'sing-box'。`);
    process.exit(1);
  }

  console.log(`开始拉取订阅并渲染配置...`);
  console.log(`类型: ${type}`);
  console.log(`输出: ${outputPath}`);
  console.log('');

  await pullAndRenderSubscription(type, outputPath);
}

// Run the CLI if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('意外错误:', error);
    process.exit(1);
  });
}

export { pullAndRenderSubscription, loadEnvironmentVariables };