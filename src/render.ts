import SingBoxTemplate from './tpl/sing-box';
import { SingBoxOutbound } from './types';

export const renderForSingbox = (subscriptionNodes: SingBoxOutbound[]) => {
  const res = {
    ...SingBoxTemplate,
  };
  // Filter SG Node only
  subscriptionNodes = subscriptionNodes.filter((node) => {
    return node.tag?.includes('新加坡');
  });
  // Type assertion to handle the compatibility between subscription nodes and template outbounds
  res.outbounds = [...subscriptionNodes, ...res.outbounds.map((item) => {
    if (item.tag === 'auto') {
        return {
          ...item,
          outbounds: subscriptionNodes.map((node) => node.tag).filter((tag) => !tag.includes('IPLC')),
        };
    } else if (item.tag === 'proxy') {
        return {
          ...item,
          outbounds: ['auto', ...subscriptionNodes.map((node) => node.tag)],
        };
    }
    return item;
  })] as SingBoxOutbound[];
  return res;
};
