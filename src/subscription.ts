import { SingBoxOutbound, SubscriptionResponse } from './types';

const SUBSCRIPTION_URL = {
  enet: 'https://enet.noahgao.online/api/v1/client/subscribe?token=$ENET_TOKEN',
  // pokemon: 'https://link01.521pokemon.com/api/v1/client/subscribe?token=$POKEMON_TOKEN',
};

export const getMergedSubscriptionNodes = async (
  type: 'sing-box',
  secretMap: Record<string, string>,
): Promise<SingBoxOutbound[]> => {
  if (type === 'sing-box') {
    const subscriptionList = await Promise.all(
      Object.entries(SUBSCRIPTION_URL).map(async ([id, url]) => {
        const tokenKey = `${id.toUpperCase()}_TOKEN`;
        const tokenValue = secretMap[tokenKey];
        const finalUrl = url.replace(new RegExp(`\\$${tokenKey}`, 'g'), tokenValue);
        
        const response = await fetch(finalUrl, {
          headers: {
            'User-Agent': `sub-dream/1.0 (sing-box)`,
          },
        });

        if (!response.ok) {
          const responseText = await response.text();
          throw new Error(
            `Failed to fetch subscription from ${url}: ${response.status} ${responseText}, headers: ${JSON.stringify(
              response.headers,
            )}`,
          );
        }

        const data = (await response.json()) as SubscriptionResponse;
        return data;
      }),
    );

    const mergedNodes = subscriptionList.flatMap(
      (subscription) =>
        subscription.outbounds?.filter(
          (outbound) =>
            outbound &&
            outbound.type &&
            !['urltest', 'selector', 'direct', 'block'].includes(outbound.type),
        ) || [],
    );

    return mergedNodes;
  }
  return [];
};
