export default {
  dns: {
    rules: [
      {
        clash_mode: 'global',
        server: 'cloudflare',
      },
      {
        clash_mode: 'direct',
        server: 'local',
      },
      {
        rule_set: 'geosite-geolocation-cn',
        server: 'local',
      },
      {
        type: 'logical',
        mode: 'and',
        rules: [
          {
            rule_set: 'geosite-geolocation-!cn',
            invert: true,
          },
          {
            rule_set: 'geoip-cn',
          },
        ],
        server: 'cloudflare',
        client_subnet: '************/24', // Any China client IP address
      },
    ],
    servers: [
      {
        tag: 'cloudflare',
        type: 'h3',
        server: '*******',
        detour: 'proxy',
      },
      {
      tag: 'dnspod',
        type: 'h3',
        server: '************',
        detour: 'direct',
      },
      {
        tag: 'local',
        type: 'local',
      },
    ],
    strategy: 'prefer_ipv4',
    cache_capacity: 2048,
  },
  inbounds: [
    {
      type: 'tun',
      address: ['**********/30', 'fdfe:dcba:9876::1/126'],
      auto_route: true,
      auto_redirect: true, // On linux
      strict_route: true,
    },
    {
      domain_strategy: 'prefer_ipv4',
      listen: '127.0.0.1',
      listen_port: 2333,
      tag: 'mixed-in',
      type: 'mixed',
    },
  ],
  outbounds: [
    {
      tag: 'proxy',
      type: 'selector',
      default: 'auto',
      outbounds: ['auto'],
    },
    {
      tag: 'auto',
      type: 'urltest',
      outbounds: [],
    },
    {
      type: 'direct',
      tag: 'direct',
    },
  ],
  route: {
    rules: [
      {
        action: 'resolve',
        strategy: 'prefer_ipv4',
      },
      {
        action: 'sniff',
      },
      {
        protocol: 'dns',
        action: 'hijack-dns',
      },
      {
        type: 'logical',
        mode: 'or',
        rules: [
          {
            port: 853,
          },
          {
            network: 'udp',
            port: 443,
          },
          {
            protocol: 'stun',
          },
        ],
        action: 'reject',
      },
      {
        clash_mode: 'direct',
        outbound: 'direct',
      },
      {
        clash_mode: 'global',
        outbound: 'proxy',
      },
      {
        ip_is_private: true,
        outbound: 'direct',
      },
      {
        rule_set: 'geosite-geolocation-cn',
        outbound: 'direct',
      },
      {
        type: 'logical',
        mode: 'and',
        rules: [
          {
            rule_set: 'geoip-cn',
          },
          {
            rule_set: 'geosite-geolocation-!cn',
            invert: true,
          },
        ],
        outbound: 'direct',
      },
    ],
    final: 'proxy',
    default_domain_resolver: 'local',
    auto_detect_interface: true,
    rule_set: [
      {
        type: 'remote',
        tag: 'geoip-cn',
        format: 'binary',
        url: 'https://raw.githubusercontent.com/SagerNet/sing-geoip/rule-set/geoip-cn.srs',
      },
      {
        type: 'remote',
        tag: 'geosite-geolocation-!cn',
        format: 'binary',
        url: 'https://raw.githubusercontent.com/SagerNet/sing-geosite/rule-set/geosite-geolocation-!cn.srs',
      },
      {
        type: 'remote',
        tag: 'geosite-geolocation-cn',
        format: 'binary',
        url: 'https://raw.githubusercontent.com/SagerNet/sing-geosite/rule-set/geosite-geolocation-cn.srs',
      },
    ],
  },
  experimental: {
    cache_file: {
      enabled: true,
      store_rdrc: true,
    },
    clash_api: {
      default_mode: 'Enhanced',
    },
  },
};
