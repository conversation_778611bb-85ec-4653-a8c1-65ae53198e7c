# Sub-Dream

一个用于拉取和处理订阅配置的工具，支持生成 sing-box 配置文件。

## 功能特性

- 🚀 支持多个订阅源的合并
- 📦 可编译为单一二进制文件
- 🔧 支持 sing-box 配置格式
- 🌐 支持 Cloudflare Workers 部署
- 🛠️ 命令行工具和 Web API 双模式

## 安装和使用

### 开发环境

```bash
# 安装依赖
bun install

# 开发模式 (Cloudflare Workers)
bun run dev

# 开发模式 (本地 Bun)
bun run dev:bun

# 部署到 Cloudflare Workers
bun run deploy
```

### CLI 工具

#### 构建二进制文件

```bash
# 构建单一二进制文件
bun run build:cli
```

这将生成一个名为 `sub-dream` 的可执行文件。

#### 配置环境变量

复制 `.env.example` 为 `.env` 并填入你的订阅 token：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
ENET_TOKEN=your_actual_token_here
```

#### 使用 CLI

```bash
# 显示帮助信息
./sub-dream --help

# 生成默认配置文件 (config.json)
./sub-dream

# 指定输出文件路径
./sub-dream -o /path/to/your/config.json

# 指定配置类型和输出路径
./sub-dream -t sing-box -o config.json
```

#### CLI 选项

- `-o, --output <path>`: 输出文件路径 (默认: config.json)
- `-t, --type <type>`: 配置类型 (默认: sing-box)
- `-h, --help`: 显示帮助信息

### Web API

部署到 Cloudflare Workers 后，可以通过 HTTP API 获取配置：

```bash
# 获取 sing-box 配置
curl "https://your-worker.your-subdomain.workers.dev/api/manifest?token=YOUR_SECRET"
```

## 开发

### 类型生成

```bash
# 生成 Cloudflare Workers 类型定义
bun run cf-typegen
```

### 项目结构

```
src/
├── cli.ts          # CLI 工具入口
├── index.ts        # Cloudflare Workers 入口
├── subscription.ts # 订阅拉取逻辑
├── render.ts       # 配置渲染逻辑
├── types.ts        # TypeScript 类型定义
└── tpl/           # 配置模板
    └── sing-box.ts # sing-box 配置模板
```

## 许可证

MITndings }>()
```

## Deno version

```
deno task start
```

